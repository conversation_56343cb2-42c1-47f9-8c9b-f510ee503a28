import hashlib
import os
import pickle
import sqlite3
import subprocess
import sys
import tempfile
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Any, Dict, List, Optional

import jwt
import uvicorn
import yaml
from fastapi import Depends, FastAPI, File, HTTPException, Request, Response, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from fastapi.security import H<PERSON>PAuthorizationCredentials, HTTPBearer
from pydantic import BaseModel, validator

# Initialize FastAPI app
app = FastAPI(
    title="SecureAPI",
    description="A secure API for file management and user operations",
    version="1.0.0",
)

# CORS middleware - Vulnerability 1: Overly permissive CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Should be specific domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security scheme
security = HTTPBearer()

# Database setup
DATABASE_PATH = "app.db"


def init_db():
    """Initialize the database with user and file tables"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Vulnerability 2: SQL injection potential in dynamic table creation
    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            email TEXT,
            role TEXT DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    )

    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY,
            filename TEXT NOT NULL,
            filepath TEXT NOT NULL,
            owner_id INTEGER,
            size INTEGER,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (owner_id) REFERENCES users (id)
        )
    """
    )

    # Create default admin user with weak password
    admin_password = hashlib.md5(
        "admin123".encode()
    ).hexdigest()  # Vulnerability 3: MD5 hashing
    cursor.execute(
        "INSERT OR IGNORE INTO users (username, password_hash, email, role) VALUES (?, ?, ?, ?)",
        ("admin", admin_password, "<EMAIL>", "admin"),
    )

    conn.commit()
    conn.close()


# Pydantic models
class UserCreate(BaseModel):
    username: str
    password: str
    email: Optional[str] = None

    @validator("password")
    def validate_password(cls, v):
        # Vulnerability 4: Weak password validation
        if len(v) < 4:  # Should be much stronger
            raise ValueError("Password too short")
        return v


class UserLogin(BaseModel):
    username: str
    password: str


class FileInfo(BaseModel):
    filename: str
    size: int
    upload_date: str


class ConfigUpdate(BaseModel):
    config_data: str  # Will accept YAML/JSON


# JWT Configuration - Vulnerability 5: Weak JWT secret
JWT_SECRET = "secret123"  # Should be a strong, random secret
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 24


def create_jwt_token(user_data: dict) -> str:
    """Create JWT token for user"""
    payload = {
        "user_id": user_data["id"],
        "username": user_data["username"],
        "role": user_data["role"],
        "exp": datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS),
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)


def verify_jwt_token(token: str) -> dict:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> dict:
    """Get current user from JWT token"""
    return verify_jwt_token(credentials.credentials)


def get_db_connection():
    """Get database connection"""
    return sqlite3.connect(DATABASE_PATH)


# Vulnerability 6: Insecure direct object reference in user lookup
def get_user_by_id(user_id: int) -> Optional[dict]:
    """Get user by ID without proper authorization checks"""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
    user = cursor.fetchone()
    conn.close()

    if user:
        return {
            "id": user[0],
            "username": user[1],
            "password_hash": user[2],  # Vulnerability: Exposing password hash
            "email": user[3],
            "role": user[4],
            "created_at": user[5],
        }
    return None


# API Endpoints


@app.on_event("startup")
async def startup_event():
    """Initialize database on startup"""
    init_db()


@app.post("/register")
async def register_user(user: UserCreate):
    """Register a new user"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Hash password using MD5 (Vulnerability 3: Weak hashing)
    password_hash = hashlib.md5(user.password.encode()).hexdigest()

    try:
        cursor.execute(
            "INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)",
            (user.username, password_hash, user.email),
        )
        conn.commit()
        user_id = cursor.lastrowid
        conn.close()

        return {"message": "User registered successfully", "user_id": user_id}
    except sqlite3.IntegrityError:
        conn.close()
        raise HTTPException(status_code=400, detail="Username already exists")


@app.post("/login")
async def login_user(user: UserLogin):
    """Login user and return JWT token"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Vulnerability 7: SQL injection in login query
    query = f"SELECT * FROM users WHERE username = '{user.username}'"
    cursor.execute(query)
    db_user = cursor.fetchone()
    conn.close()

    if not db_user:
        raise HTTPException(status_code=401, detail="Invalid credentials")

    # Check password
    password_hash = hashlib.md5(user.password.encode()).hexdigest()
    if db_user[2] != password_hash:
        raise HTTPException(status_code=401, detail="Invalid credentials")

    user_data = {"id": db_user[0], "username": db_user[1], "role": db_user[4]}

    token = create_jwt_token(user_data)
    return {"access_token": token, "token_type": "bearer"}


@app.get("/users/{user_id}")
async def get_user(user_id: int, current_user: dict = Depends(get_current_user)):
    """Get user information - Vulnerability 6: No authorization check"""
    user = get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Vulnerability: Returns sensitive information including password hash
    return user


@app.get("/profile")
async def get_profile(current_user: dict = Depends(get_current_user)):
    """Get current user's profile"""
    user = get_user_by_id(current_user["user_id"])
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Remove password hash from response (good practice)
    user.pop("password_hash", None)
    return user


@app.post("/upload")
async def upload_file(
    file: UploadFile = File(...), current_user: dict = Depends(get_current_user)
):
    """Upload a file - Multiple vulnerabilities"""

    # Vulnerability 8: No file type validation
    # Vulnerability 9: No file size limits
    # Vulnerability 10: Path traversal vulnerability

    upload_dir = Path("uploads")
    upload_dir.mkdir(exist_ok=True)

    # Vulnerability: Direct use of filename without sanitization
    file_path = upload_dir / file.filename

    # Save file
    content = await file.read()
    with open(file_path, "wb") as f:
        f.write(content)

    # Save file info to database
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute(
        "INSERT INTO files (filename, filepath, owner_id, size) VALUES (?, ?, ?, ?)",
        (file.filename, str(file_path), current_user["user_id"], len(content)),
    )
    conn.commit()
    file_id = cursor.lastrowid
    conn.close()

    return {
        "message": "File uploaded successfully",
        "file_id": file_id,
        "filename": file.filename,
        "size": len(content),
    }


@app.get("/download/{file_id}")
async def download_file(file_id: int, current_user: dict = Depends(get_current_user)):
    """Download a file - Vulnerability 11: No access control"""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM files WHERE id = ?", (file_id,))
    file_record = cursor.fetchone()
    conn.close()

    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")

    # Vulnerability: No check if current user owns the file
    file_path = file_record[2]

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found on disk")

    return FileResponse(file_path, filename=file_record[1])


@app.get("/files")
async def list_files(current_user: dict = Depends(get_current_user)):
    """List user's files"""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute(
        "SELECT id, filename, size, upload_date FROM files WHERE owner_id = ?",
        (current_user["user_id"],),
    )
    files = cursor.fetchall()
    conn.close()

    return [
        {"id": f[0], "filename": f[1], "size": f[2], "upload_date": f[3]} for f in files
    ]


@app.post("/execute")
async def execute_command(
    request: Request, current_user: dict = Depends(get_current_user)
):
    """Execute system command - Vulnerability 12: Command injection"""
    data = await request.json()
    command = data.get("command", "")

    # Vulnerability: Direct command execution without validation
    if current_user["role"] == "admin":
        try:
            # Extremely dangerous - direct shell execution
            result = subprocess.run(
                command,
                shell=True,  # Vulnerability: shell=True enables injection
                capture_output=True,
                text=True,
                timeout=30,
            )
            return {
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode,
            }
        except subprocess.TimeoutExpired:
            raise HTTPException(status_code=408, detail="Command timeout")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    else:
        raise HTTPException(status_code=403, detail="Admin access required")


@app.post("/config/update")
async def update_config(
    config: ConfigUpdate, current_user: dict = Depends(get_current_user)
):
    """Update application configuration - Vulnerability 13: Deserialization"""
    if current_user["role"] != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        # Vulnerability: Unsafe YAML loading allows code execution
        config_data = yaml.load(config.config_data, Loader=yaml.Loader)

        # Save config to file
        config_file = Path("app_config.yaml")
        with open(config_file, "w") as f:
            yaml.dump(config_data, f)

        return {"message": "Configuration updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid configuration: {str(e)}")


@app.get("/backup")
async def create_backup(current_user: dict = Depends(get_current_user)):
    """Create database backup - Vulnerability 14: Insecure serialization"""
    if current_user["role"] != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    conn = get_db_connection()
    cursor = conn.cursor()

    # Get all data
    cursor.execute("SELECT * FROM users")
    users = cursor.fetchall()
    cursor.execute("SELECT * FROM files")
    files = cursor.fetchall()
    conn.close()

    backup_data = {
        "users": users,
        "files": files,
        "timestamp": datetime.utcnow().isoformat(),
    }

    # Vulnerability: Using pickle for serialization (allows code execution)
    backup_file = tempfile.NamedTemporaryFile(delete=False, suffix=".backup")
    with open(backup_file.name, "wb") as f:
        pickle.dump(backup_data, f)

    return FileResponse(
        backup_file.name,
        filename=f"backup_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.backup",
    )


@app.post("/restore")
async def restore_backup(
    file: UploadFile = File(...), current_user: dict = Depends(get_current_user)
):
    """Restore database from backup - Vulnerability 15: Unsafe deserialization"""
    if current_user["role"] != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        content = await file.read()

        # Vulnerability: Unsafe pickle loading allows code execution
        backup_data = pickle.loads(content)

        # Clear existing data and restore
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM files")
        cursor.execute("DELETE FROM users")

        # Restore users
        for user in backup_data["users"]:
            cursor.execute(
                "INSERT INTO users (id, username, password_hash, email, role, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                user,
            )

        # Restore files
        for file_record in backup_data["files"]:
            cursor.execute(
                "INSERT INTO files (id, filename, filepath, owner_id, size, upload_date) VALUES (?, ?, ?, ?, ?, ?)",
                file_record,
            )

        conn.commit()
        conn.close()

        return {"message": "Database restored successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Restore failed: {str(e)}")


@app.get("/logs")
async def get_logs(request: Request, current_user: dict = Depends(get_current_user)):
    """Get application logs - Vulnerability 16: Information disclosure"""
    if current_user["role"] != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    # Vulnerability: Exposing sensitive information in logs
    log_file = request.query_params.get("file", "app.log")

    # Vulnerability 17: Path traversal in log file access
    try:
        with open(log_file, "r") as f:
            logs = f.read()
        return {"logs": logs}
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Log file not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/debug")
async def debug_info(current_user: dict = Depends(get_current_user)):
    """Debug endpoint - Vulnerability 18: Information disclosure"""
    if current_user["role"] != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    # Vulnerability: Exposing sensitive system information
    debug_data = {
        "environment_variables": dict(os.environ),  # Exposes secrets
        "current_directory": os.getcwd(),
        "python_path": sys.path,
        "jwt_secret": JWT_SECRET,  # Extremely dangerous
        "database_path": DATABASE_PATH,
        "process_id": os.getpid(),
        "user_info": os.getlogin() if hasattr(os, "getlogin") else "unknown",
    }

    return debug_data


@app.get("/search")
async def search_users(q: str, current_user: dict = Depends(get_current_user)):
    """Search users - Vulnerability 19: SQL injection in search"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Vulnerability: SQL injection through search parameter
    query = f"SELECT id, username, email, role FROM users WHERE username LIKE '%{q}%' OR email LIKE '%{q}%'"
    cursor.execute(query)
    results = cursor.fetchall()
    conn.close()

    return [
        {"id": r[0], "username": r[1], "email": r[2], "role": r[3]} for r in results
    ]


# Health check endpoint (good practice)
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}


if __name__ == "__main__":
    # Vulnerability 20: Debug mode enabled in production
    uvicorn.run(
        app,
        host="0.0.0.0",  # Vulnerability 21: Binding to all interfaces
        port=8000,
        debug=True,  # Should be False in production
        reload=True,  # Should be False in production
    )
