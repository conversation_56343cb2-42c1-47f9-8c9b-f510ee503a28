import subprocess
import sys


def run_opengrep(path):
    config = "auto"
    try:
        # Run opengrep with -auto flag in the specified path
        cmd = ["opengrep", "--json", "--config", config]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)

        # Print the output
        print(result.stdout)

        # If there are any errors, print them
        if result.stderr:
            print("Errors:", result.stderr, file=sys.stderr)

    except subprocess.CalledProcessError as e:
        print(f"Error running opengrep: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}", file=sys.stderr)
        # Save error details to JSON file
        import datetime
        import json

        error_log = {
            "timestamp": datetime.datetime.now().isoformat(),
            "error": str(e),
            "type": e.__class__.__name__,
        }
        with open("opengrep_errors.json", "w") as f:
            json.dump(error_log, f, indent=2)
        print(f"Error details saved to opengrep_errors.json", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python run_opengrep.py <path>")
        sys.exit(1)

    path = sys.argv[1]
    run_opengrep(path)
